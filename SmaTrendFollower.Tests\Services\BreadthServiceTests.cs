using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for BreadthService
/// Tests market breadth analysis, sentiment determination, and caching functionality
/// </summary>
public class BreadthServiceTests : IDisposable
{
    private readonly Mock<IDynamicUniverseProvider> _mockUniverseProvider;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IMarketRegimeService> _mockRegimeService;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<BreadthService>> _mockLogger;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly BreadthService _breadthService;

    public BreadthServiceTests()
    {
        _mockUniverseProvider = new Mock<IDynamicUniverseProvider>();
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockRegimeService = new Mock<IMarketRegimeService>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<BreadthService>>();
        _mockDatabase = new Mock<IDatabase>();

        // Setup configuration
        var configSection = new Mock<IConfigurationSection>();
        _mockConfiguration.Setup(x => x.GetSection("BreadthService")).Returns(configSection.Object);

        // Setup Redis
        _mockRedisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>()))
            .ReturnsAsync(_mockDatabase.Object);

        // Setup universe provider
        _mockUniverseProvider.Setup(x => x.GetUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { "AAPL", "MSFT", "GOOGL" });

        _breadthService = new BreadthService(
            _mockUniverseProvider.Object,
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            _mockRegimeService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Act & Assert - Constructor should not throw
        var service = new BreadthService(
            _mockUniverseProvider.Object,
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            _mockRegimeService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        service.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithNullUniverseProvider_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new BreadthService(
            null!,
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            _mockRegimeService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("universeProvider");
    }

    [Fact]
    public void Constructor_WithNullMarketDataService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new BreadthService(
            _mockUniverseProvider.Object,
            null!,
            _mockRedisService.Object,
            _mockRegimeService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("marketDataService");
    }

    [Fact]
    public void Constructor_WithNullRedisService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new BreadthService(
            _mockUniverseProvider.Object,
            _mockMarketDataService.Object,
            null!,
            _mockRegimeService.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("redisService");
    }

    [Fact]
    public void Constructor_WithNullRegimeService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new BreadthService(
            _mockUniverseProvider.Object,
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            null!,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("regimeService");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new BreadthService(
            _mockUniverseProvider.Object,
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            _mockRegimeService.Object,
            _mockConfiguration.Object,
            null!);

        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public async Task GetCachedBreadthAsync_WithNoCache_ShouldReturnNull()
    {
        // Arrange
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _breadthService.GetCachedBreadthAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CalculateMarketBreadthAsync_WithValidData_ShouldReturnAnalysis()
    {
        // Arrange
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        // Setup market data with advancing stocks
        var bars = new List<IBar>
        {
            CreateMockBar(100m, DateTime.Today.AddDays(-1)),
            CreateMockBar(105m, DateTime.Today) // 5% advance
        };

        _mockMarketDataService.Setup(x => x.GetBarsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(bars);

        // Act
        var result = await _breadthService.CalculateMarketBreadthAsync();

        // Assert
        result.Should().NotBeNull();
        result.Sentiment.Should().BeOneOf(MarketRiskSentiment.RiskOn, MarketRiskSentiment.Neutral, MarketRiskSentiment.RiskOff);
        result.BreadthScore.Should().BeInRange(0m, 100m);
    }

    [Fact]
    public async Task GetMarketSentimentAsync_ShouldReturnValidSentiment()
    {
        // Arrange
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        var bars = new List<IBar>
        {
            CreateMockBar(100m, DateTime.Today.AddDays(-1)),
            CreateMockBar(105m, DateTime.Today)
        };

        _mockMarketDataService.Setup(x => x.GetBarsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(bars);

        // Act
        var result = await _breadthService.GetMarketSentimentAsync();

        // Assert
        result.Should().BeOneOf(MarketRiskSentiment.RiskOn, MarketRiskSentiment.Neutral, MarketRiskSentiment.RiskOff);
    }

    [Fact]
    public async Task CalculateAdvanceDeclineAsync_WithMixedData_ShouldReturnMetrics()
    {
        // Arrange
        var advancingBars = new List<IBar>
        {
            CreateMockBar(100m, DateTime.Today.AddDays(-1)),
            CreateMockBar(105m, DateTime.Today) // Advancing
        };

        var decliningBars = new List<IBar>
        {
            CreateMockBar(100m, DateTime.Today.AddDays(-1)),
            CreateMockBar(95m, DateTime.Today) // Declining
        };

        _mockMarketDataService.SetupSequence(x => x.GetBarsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(advancingBars)
            .ReturnsAsync(decliningBars)
            .ReturnsAsync(advancingBars);

        // Act
        var result = await _breadthService.CalculateAdvanceDeclineAsync();

        // Assert
        result.Should().NotBeNull();
        result.Advancers.Should().BeGreaterOrEqualTo(0);
        result.Decliners.Should().BeGreaterOrEqualTo(0);
        result.AdvanceDeclineRatio.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task ClearCacheAsync_WhenCalled_ShouldNotThrow()
    {
        // Act & Assert
        var act = async () => await _breadthService.ClearCacheAsync();
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void Dispose_WhenCalled_ShouldNotThrow()
    {
        // Act & Assert
        var act = () => _breadthService.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public async Task CalculateMarketBreadthAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _breadthService.Dispose();

        // Act & Assert
        var act = async () => await _breadthService.CalculateMarketBreadthAsync();
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    private static IBar CreateMockBar(decimal close, DateTime timestamp)
    {
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(close);
        mockBar.Setup(x => x.High).Returns(close * 1.02m);
        mockBar.Setup(x => x.Low).Returns(close * 0.98m);
        mockBar.Setup(x => x.Open).Returns(close * 0.99m);
        mockBar.Setup(x => x.TimeUtc).Returns(timestamp);
        mockBar.Setup(x => x.Volume).Returns(1000000);
        return mockBar.Object;
    }

    public void Dispose()
    {
        _breadthService?.Dispose();
    }
}
